"use client";

import { type UserRequest, UserRequestSchema } from "@/app/api/auth/users/dto/userRequest";
import ModalLayout from "@/components/shared/info-modal/ModalLayout";
import usePortalStore from "@/stores/usePortalStore";
import { compareObjects } from "@/utils/compareObjects";
import {
	Avatar,
	Button,
	Divider,
	Input,
} from "@heroui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import type React from "react";
import { useEffect, useMemo, useRef } from "react";
import { useForm, Controller } from "react-hook-form";

const emptyProfile: UserRequest = {
	name: "",
	email: "",
	phone: "",
	// image: "",
	// roles: [],
	// isEditor: false,
	// isAdmin: false,
	// isAuthor: false,
	// isGuess: false,
	// tokenLink: "",
};

export const ProfileSettings: React.FC = () => {
	const {
		handleSubmit,
		formState: { errors, isValid, isSubmitting },
		reset,
		watch,
		control,
	} = useForm<UserRequest>({
		resolver: zodResolver(UserRequestSchema),
		defaultValues: emptyProfile,
		mode: "onChange", // Enable real-time validation
	});
	const user = usePortalStore((state) => state.user);
	const currentValues = watch();
	const hasInitialized = useRef(false);

	// set initial user data
	useEffect(() => {
		if (user && !hasInitialized.current) {
			const { success: valid, data } = UserRequestSchema.safeParse(user);
			console.group("ProfileSettings useEffect");
			console.log("User data:", user);
			console.log("Current form values:", currentValues);

			if (valid && data) {
				// Create the form data with proper fallbacks for nullable fields
				const formData: UserRequest = {
					name: data.name || "",
					email: data.email || "",
					phone: data.phone || "",
				};

				console.log("Resetting form with:", formData);
				reset(formData);
				hasInitialized.current = true;
			}
			console.groupEnd();
		}
	}, [user, reset, currentValues]); // Now we can safely include currentValues

	const hasChanges = useMemo(() => {
		return user && currentValues && !compareObjects({ name: user.name, email: user.email, phone: user.phone } as UserRequest, currentValues);
	}, [currentValues, user]);

	const onSubmit = (data: UserRequest) => {
		console.log("Saving profile data:", data);
	};

	return (
		<ModalLayout title={"Profile"}>
			<form onSubmit={handleSubmit(onSubmit)}>
				<div className="flex items-center gap-4 mb-8">
					<Avatar
						name={user?.name?.charAt(0) || "U"}
						src={user?.image || undefined}
						size="lg"
						className="w-20 h-20 text-2xl"
					/>
					<div>
						<h3 className="text-lg font-medium">{user?.name || ""}</h3>
						<p className="text-default-500 text-sm">Role</p>
					</div>
				</div>

				<div className="mb-8">
					<h3 className="text-lg font-medium mb-4">Personal Info</h3>

					<div className="grid grid-cols-1 md:grid-cols-2_ gap-4">
						<div>
							<Controller
								name="name"
								control={control}
								render={({ field }) => (
									<Input
										label={"Name"}
										{...field}
										variant="bordered"
										errorMessage={errors.name?.message}
										isInvalid={!!errors.name}
									/>
								)}
							/>
							{/* <p className="text-default-500 text-xs mt-1">Your full name</p> */}
						</div>

						<div>
							<Controller
								name="email"
								control={control}
								render={({ field }) => (
									<Input
										label={"Email"}
										{...field}
										variant="bordered"
										errorMessage={errors.email?.message}
										isInvalid={!!errors.email}
									/>
								)}
							/>
						</div>
					</div>

					<div className="mt-4 mb-4">
						<Controller
							name="phone"
							control={control}
							render={({ field }) => (
								<Input
									label={"Phone Number"}
									{...field}
									variant="bordered"
									startContent={
										<div className="flex items-center">
											<span className="text-default-400 text-small">+1</span>
										</div>
									}
									placeholder="(*************"
									errorMessage={errors.phone?.message}
									isInvalid={!!errors.phone}
								/>
							)}
						/>
						<p className="text-default-500 text-xs mt-1">Your contact phone number</p>
					</div>
				</div>

				<div className="mb-8">
					<Button
						color="primary"
						variant="flat"
						type="submit"
						isDisabled={isSubmitting || !isValid || !hasChanges}
					>
						Save
					</Button>
				</div>

				<Divider className="my-8" />

				<div>
					<h3 className="text-lg font-medium mb-4">Account</h3>
					<Button
						color="danger"
						variant="solid"
						onPress={() => console.log("Delete Account")}
					>
						Delete Account
					</Button>
				</div>
			</form>
		</ModalLayout>
	);
};

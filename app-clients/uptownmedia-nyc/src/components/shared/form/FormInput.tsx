import { Input, type InputProps } from "@heroui/react";
import { forwardRef } from "react";

interface FormInputProps extends Omit<InputProps, 'onChange' | 'onBlur' | 'value' | 'name'> {
  name?: string;
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
}

/**
 * A wrapper around HeroUI Input that's compatible with React Hook Form
 * This component properly forwards refs and handles the onChange event
 * to work seamlessly with {...register("fieldName")}
 */
export const FormInput = forwardRef<HTMLInputElement, FormInputProps>(
  ({ onChange, onBlur, value, name, ...props }, ref) => {
    return (
      <Input
        {...props}
        ref={ref}
        name={name}
        value={value || ""}
        onValueChange={onChange}
        onBlur={onBlur}
      />
    );
  }
);

FormInput.displayName = "FormInput";

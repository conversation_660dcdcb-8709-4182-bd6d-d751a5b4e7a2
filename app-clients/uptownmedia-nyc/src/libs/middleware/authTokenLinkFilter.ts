import type { NextAuthRequest } from "next-auth";

import { type NextRequest, NextResponse } from "next/server";

import { UserRole } from "@/app/api/auth/users/dao/user";

const mapRedirect: Record<UserRole, string> = {
	[UserRole.GUEST]: "/",
	[UserRole.ADMIN]: "/portal/admin",
	[UserRole.EDITOR]: "/portal/editor",
	[UserRole.AUTHOR]: "/portal/author",
	[UserRole.READER]: "/",
	[UserRole.SUPERUSER]: "/portal/superuser",
};

const rolePriority: UserRole[] = [
	UserRole.SUPERUSER,
	UserRole.ADMIN,
	UserRole.EDITOR,
	UserRole.AUTHOR,
	UserRole.READER,
	UserRole.GUEST,
];

export const authTokenLinkFilter = (
	req: NextRequest,
	res: NextResponse | null,
): NextResponse | null => {
	const request = req as NextAuthRequest;
	const user = request.auth?.user;
	const roles: UserRole[] | undefined = user?.roles;
	const pathname = req.nextUrl.pathname;

	if (pathname === "/auth/sign-in") {
		if (!user) return NextResponse.next();

		if (roles && roles.length > 0) {
			const topRole = rolePriority.find((r) => roles.includes(r));

			if (topRole) {
				return NextResponse.redirect(
					new URL(mapRedirect[topRole], req.nextUrl.origin),
				);
			}
		}

		if (user.tokenLink) {
			return NextResponse.redirect(
				new URL(`/auth/sign-in/${user.tokenLink}/portal`, req.nextUrl.origin),
			);
		}
	}

	return res;
};

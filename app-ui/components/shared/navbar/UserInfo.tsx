"use client";
import type React from 'react';
import { Avatar } from "@heroui/react";
import { cn } from "@heroui/theme";
import useCustomHover from '@/utils/hooks/useCustomHover';
import { useTranslation } from "@/utils/i18n/client";
import { useLanguage } from "@/utils/i18n/LanguageContext";
import { useMemo } from "react";

interface UserInfoProps {
  user?: {
    avatar?: string;
    firstName?: string;
    lastName?: string;
  };
  isMobile: boolean;
  loadingUser: boolean;
  selectedWorkspace?: string;
  hovered?: boolean;
}

const UserInfo: React.FC<UserInfoProps> = ({ user, isMobile, loadingUser, selectedWorkspace, hovered }) => {
  const { hoverProps, isHovered } = useCustomHover();
  const { currentLanguage } = useLanguage();
  const { t } = useTranslation(currentLanguage, "workspaces");
  const showSelectedRole = useMemo(() => {
    if (!t) return false;

    const workspaceNames = t("workspaceNames", { returnObjects: true });
    return Object.keys(workspaceNames || []).includes(selectedWorkspace || "");
  }, [selectedWorkspace, t]);

  return (
    <div
      {...hoverProps}
      className={
        "flex gap-2 justify-between sm:justify-content sm:min-w-10 sm:h-10 sm:w-10 bg-transparent rounded-none px-0"
      }
    >
      {user?.avatar && <Avatar
        className={cn("bg-neutral-200 flex-none min-h-auto ")}
        isBordered={false}
        showFallback={true}
        size={
          isMobile ? "md" : "md" // this could vary based on the design
        }
        src={user?.avatar}
      />}
      {!isMobile && (
        <div className={"flex flex-1 items-center"}>
          <div className="flex max-w-full flex-col text-left hover:text-primary focus:text-primary justify-center">
            {!loadingUser &&
              <>
                {showSelectedRole && <span className={cn("text-tiny text-default-400", { "text-primary": hovered !== undefined ? hovered : isHovered })}>
                  {t(`workspaceNames.${selectedWorkspace}`)}
                </span>}
                <span className={cn("text-medium text-default-600 break-all", { "text-primary": hovered !== undefined ? hovered : isHovered })}>
                  {user?.firstName} {user?.lastName?.slice(0, 1)[0]}.
                </span>
              </>}
            {loadingUser && (
              <div className="flex flex-col gap-2 animate-pulse">
                <span className="text-default-400 h-4 w-28 bg-default-100 rounded-tiny">
                </span>
                <span className="font-medium text-default-600 h-6 w-24 bg-default-100 rounded-sm">
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default UserInfo;
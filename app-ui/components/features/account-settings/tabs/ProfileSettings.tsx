"use client";

import type React from 'react';
import { useEffect, useState, useMemo } from 'react';
import { Avatar, Button, Input, Select, SelectItem, Divider, Checkbox } from "@heroui/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslation } from '@/utils/i18n/client';
import { useLanguage } from '@/utils/i18n/LanguageContext';
import { compareObjects } from "@/utils/compareObjects";
import useAppStore from "@/stores/students/useAppStore";
import { AccountLoggedInSchema, type User, type UserLoggedIn } from "@/types/entities/user";
import { ContentWrapperBase } from "@/components/shared";

const countries = [
  { label: 'United States', value: 'United States' },
  { label: 'Canada', value: 'Canada' },
  { label: 'United Kingdom', value: 'United Kingdom' },
  { label: 'Australia', value: 'Australia' },
  { label: 'Germany', value: 'Germany' },
  { label: 'France', value: 'France' },
  { label: 'Japan', value: 'Japan' }
];


const emptyProfile: UserLoggedIn = {
  id: '',
  firstName: '',
  lastName: '',
  phoneNumber: '',
  email: '',
  consentToReceiveMessages: false,
  invitationCode: '',
  roles: [],
  avatar: '',
  // Address fields
  country: '',
  address: '',
  city: '',
  postalCode: '',
  // Base fields
  createdAt: '',
  createdBy: '',
  updatedAt: '',
  updatedBy: '',
  deletedAt: '',
  deletedBy: '',
  deleted: false
};

export const ProfileSettings: React.FC = () => {
  const [initialProfile, setInitialProfile] =
    useState<User>(emptyProfile);
  const user = usePortalStore((state) => state.user);
  const {
    register,
    handleSubmit,
    formState: { errors, isValid, isSubmitting },
    reset,
    clearErrors,
    setValue,
    watch,
  } = useForm<User>({
    resolver: zodResolver(UserSchema),
    defaultValues: initialProfile,
  });
  const currentValues = watch();

  useEffect(() => {
    const areTheSame = user && compareObjects(emptyProfile, user);
    console.group("ProfileSettings useEffect");
    console.log("values beofre sets:", currentValues);
    if (user && !areTheSame && reset) {
      console.log("setting up the form");
      setInitialProfile(user);
      reset(user);
    }
    console.log("values after sets:", currentValues);
    console.groupEnd();
    // return () => {
    //   setInitialProfile(emptyProfile);
    //   reset(emptyProfile);
    //   clearErrors();
    // }
  }, [user, reset, clearErrors]);

  const hasChanges = useMemo(() => {
    return user && !compareObjects(user, currentValues);
  }, [currentValues, user]);

  const onSubmit = (data: UserLoggedIn) => {
    console.log('Saving profile data for user:', user?.id, data);
    // Here you would typically make an API call to update the user's profile
    // Example: updateUserProfile(user?.id, data).then(() => setInitialProfile(data));
  };

  const isAdmin = user?.roles?.includes('Admin');

  return (
    <ContentWrapperBase
      title={t('profile.title')}
    // className='border-none'
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="flex items-center gap-4 mb-8">
          <Avatar
            name={user?.name?.charAt(0) || "U"}
            src={user?.image || undefined}
            size="lg"
            className="w-20 h-20 text-2xl"
          />
          <div>
            <h3 className="text-lg font-medium">{user?.name || ""}</h3>
            <p className="text-default-500 text-sm">Role</p>
          </div>
        </div>

        <div className="mb-8">
          <h3 className="text-lg font-medium mb-4">Personal Info</h3>

          <div className="grid grid-cols-1 md:grid-cols-2_ gap-4">
            <div>
              <Input
                label={"Name"}
                {...register("name")}
                variant="bordered"
                errorMessage={errors.name?.message}
                isInvalid={!!errors.name}
              />
              <p className="text-default-500 text-xs mt-1">Your full name</p>
            </div>

            <div>
              <Input
                label={"Email"}
                {...register("email")}
                variant="bordered"
                errorMessage={errors.email?.message}
                isInvalid={!!errors.email}
              />
            </div>
          </div>

          <div className="mt-4 mb-4">
            <Input
              label={"Phone Number"}
              {...register("phone")}
              variant="bordered"
              startContent={
                <div className="flex items-center">
                  <span className="text-default-400 text-small">+1</span>
                </div>
              }
              placeholder="(*************"
              errorMessage={errors.phone?.message}
              isInvalid={!!errors.phone}
            />
            <p className="text-default-500 text-xs mt-1">Your contact phone number</p>
          </div>
        </div>

        <div className="mb-8">
          <Button
            color="primary"
            variant="flat"
            type="submit"
            isDisabled={isSubmitting || !isValid || !hasChanges}
          >
            Save
          </Button>
        </div>

        <Divider className="my-8" />

        <div>
          <h3 className="text-lg font-medium mb-4">Account</h3>
          <Button
            color="danger"
            variant="solid"
            onPress={() => console.log("Delete Account")}
          >
            Delete Account
          </Button>
        </div>
      </form>
    </ContentWrapperBase>
  );
};
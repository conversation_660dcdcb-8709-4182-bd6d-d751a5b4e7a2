{"name": "@skoolscout-com/app-ui", "version": "0.21.1", "private": true, "scripts": {"dev": "next dev --turbo", "dev:iym": "next dev -H iym.skoolscout.local --turbo", "debug": "NODE_OPTIONS='--inspect' next dev", "build": "node --trace-warnings ./node_modules/.bin/next build && sh ./prepare-build.sh", "clean-all": "rm -rf .next node_modules", "start": "next start", "lint": "npx @biomejs/biome check --write .", "format": "npx @biomejs/biome format --write .", "lint:fix": "npx @biomejs/biome lint --write .", "gen-dotenv:list": "npx gen-dotenv list-env develop ", "gen-dotenv:gen": "npx gen-dotenv generate-env develop", "storybook": "concurrently 'npm:watch:*'", "watch:tailwind": "npx tailwindcss -i ./styles/globals.css -o .storybook/storybook.css --watch", "build:tailwind": "npx tailwindcss -i ./styles/globals.css -o .storybook/storybook.css", "watch:storybook": "storybook dev -p 6006 - s ./public", "build-storybook": "npm run build:tailwind && storybook build "}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.803.0", "@aws-sdk/lib-dynamodb": "^3.804.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/pro-regular-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@heroui/button": "2.2.17", "@heroui/code": "2.2.12", "@heroui/input": "2.4.17", "@heroui/kbd": "2.2.13", "@heroui/link": "2.2.14", "@heroui/listbox": "2.3.16", "@heroui/navbar": "2.2.15", "@heroui/react": "2.7.6", "@heroui/snippet": "2.2.18", "@heroui/switch": "2.2.15", "@heroui/system": "2.4.13", "@heroui/theme": "2.4.13", "@heroui/tooltip": "2.2.14", "@hookform/resolvers": "^3.10.0", "@react-aria/ssr": "3.9.7", "@react-aria/visually-hidden": "3.8.18", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.64.2", "bcryptjs": "^3.0.2", "clsx": "2.1.1", "concurrently": "^9.1.2", "framer-motion": "~11.17.0", "intl-messageformat": "^10.7.11", "next": "15.2.3", "next-auth": "5.0.0-beta.15", "next-themes": "^0.4.4", "react": "19.0.0", "react-dom": "19.0.0", "react-google-recaptcha": "^3.1.0", "react-haiku": "^2.2.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-query": "^3.39.3", "react-responsive": "^10.0.0", "sharp": "^0.33.5", "swiper": "^11.2.2", "uuidv7": "^1.0.2", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@chromatic-com/storybook": "^3.2.4", "@exa-online/storybook-jira-addon": "^3.0.3", "@skoolscout/gen-dotenv": "0.4.2", "@storybook/addon-a11y": "^8.5.0", "@storybook/addon-console": "^3.0.0", "@storybook/addon-coverage": "^1.0.5", "@storybook/addon-designs": "^8.0.4", "@storybook/addon-essentials": "^8.5.3", "@storybook/addon-interactions": "^8.5.3", "@storybook/addon-links": "^8.5.0", "@storybook/addon-onboarding": "^8.5.3", "@storybook/addon-storysource": "^8.5.0", "@storybook/addon-themes": "^8.5.0", "@storybook/addon-viewport": "^8.5.0", "@storybook/blocks": "^8.5.3", "@storybook/nextjs": "^8.5.3", "@storybook/react": "^8.5.3", "@storybook/test": "^8.5.3", "@testing-library/react": "^16.3.0", "@types/node": "22.10.5", "@types/react": "19.0.4", "@types/react-dom": "19.0.2", "@types/react-google-recaptcha": "^2.1.9", "autoprefixer": "10.4.20", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.4", "i18next-resources-to-backend": "^1.2.1", "msw": "^2.7.0", "msw-storybook-addon": "^2.0.4", "picocolors": "^1.1.1", "postcss": "8.4.49", "react-cookie": "^7.2.2", "react-i18next": "^15.4.1", "storybook": "^8.5.3", "storybook-addon-grid-overlay": "^0.0.9", "storybook-zeplin": "^3.0.0", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.17", "typescript": "5.7.3"}}
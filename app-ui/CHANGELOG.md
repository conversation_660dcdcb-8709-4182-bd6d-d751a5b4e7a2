# @skoolscout-com/app-ui

## 0.21.1

### Patch Changes

- 5bb9f9a: Changes:
  0e18ccf Refactor: Remove unused GroupResponse import in groupMemberServices
  cfeb4ae Refactor: Update group member and group request schemas improve role-based sidebar filtering Added a few new feature from made from uptownmedia
  2d44ff3 Refactor: Update GroupMembers and ProfileSettings imports and create new components

## 0.21.0

### Minor Changes

- 8794a7c: Changes:
  1a9ea37 feat: Update feature flag documentation to include initialization and configurable environments
  2ba110d feat: Implement configurable environments for feature flags and enhance context management
  0c2eee4 feat: Enhance feature flag management by updating environment handling and improving UI responsiveness
  b02f070 feat: Replace button with Button component in FeatureFlagHierarchy and remove status label in FeatureFlagItem
  6e5b0fc feat: Add feature flag management components and services
  050c8bc feat: imrprove feature flag service

## 0.20.0

### Minor Changes

- 79e7dcf: Changes:
  c7e7973 feat: Add feature flags demo page and enhance feature flags management interface
  2da923c feat: Update feature flag documentation to include hierarchical flags and remove redundant file
  cbf5111 feat: Add hierarchical feature flags for analytics dashboard
  55e8fa3 feat: Implement feature flag system with context and hooks

## 0.19.4

### Patch Changes

- 9c9dd87: Changes:
  dda7b57 feat: implement mock data for groups and group members, update services and components to utilize new data structure

## 0.19.3

### Patch Changes

- 3e4d559: Changes:
  cc8765e chore/updating data structure

## 0.19.2

### Patch Changes

- 0b86be5: Changes:
  33c0654 feat: implement InfoModal component and related modal functionality

## 0.19.1

### Patch Changes

- 6562175: Changes:
  eb74a7a Add "use client" directive to LanguageAccordion and useUser hooks for client-side rendering
  011eb8a Refactor settings pages to use ContentWrapperBase for consistent layout
  17ac31f Refactor profile dropdown and URL tab link to use dynamic parameter names for improved flexibility
  df68aac Refactor account settings: remove privacy policy and terms of use pages, replace AccountSettingsModal with InfoModal, and update ProfileDropdown links to use UrlTabLink components.
  a7e051b Enhance workspace selection logic and sidebar functionality

## 0.19.0

### Minor Changes

- eed4e00: Changes:
  228521b Add new settings pages and enhance portal layout and sidebar functionality - Added new settings pages under `/portal/settings/`: - Profile - Email & Password - Family Members - Team Members - Stored Credentials - Preferences - Notifications - Privacy Policy - Terms of Use - Introduced `AccountSettingsLayout` and updated `WorkspaceLayout` to support the new settings pages. - Updated `GroupMembersTab` to `GroupMembers` and refactored its usage. - Enhanced sidebar and navbar components: - Added translations for new settings items in `sidebar.json` (English and Spanish). - Updated `Sidebar` and `Navbar` components to include new settings links. - Refactored `ProfileSettingsTab` and `GroupMembers` components for better modularity and integration. - Adjusted `WorkspaceLayout` to handle compact and overlay modes dynamically based on viewport width and user preferences. - Added support for tenant-specific logos in `WorkspaceLayout`. - Improved user experience with dynamic sidebar and navbar content based on user roles and workspace context.

## 0.18.3

### Patch Changes

- 125690c: Changes:
  70641eb Fix hasChanges logic to ensure user is defined before comparison
  f5e1a77 Refactor profile settings: update state management and add consent checkbox; enhance localization for first name, last name, and consent message

## 0.18.2

### Patch Changes

- 09eb27a: Changes:
  35e480e Refactor ProfileSettingsTab and user service to enhance user profile management and add address fields
  40562de Enhance role-based filtering in ProfileDropdown for user menu items
  2a0a5d3 Refactor group member retrieval logic and improve user state management in useUser hook
  cfb9548 Refactor GroupMembersTab to use dynamic group type in titles Improve group member retrieval logic to showcase family and team members
  61c7b2a Remove console log for group members response in GroupMembersTab component
  b8742ba Refactor GroupMembersTab and AccountSettings components to improve role-based member retrieval and enhance sidebar functionality - Move out of AccountSettingSidebar the logic to filter the menu items - Merge the 2 objects I was using to handle the Role-Based Permission Menu for the account settings modal and sidebar
  2e07a61 Refactor account settings components to enhance role-based permissions and improve user experience - Show team and family group members based on all he roles and not in a single selected one

## 0.18.1

### Patch Changes

- 3b39af7: Changes:
  15025ae Aadjust group member types for better clarity and localization Update error logging to include role
  27040fc feat: Refactor group member services and schemas to support role-based member retrieval
  d155d0c feat: Integrate user profile data into ProfileSettingsTab component
  d49f70e feat: Enhance group member management with new API endpoints and localization support - return dummy data

## 0.18.0

### Minor Changes

- 4266441: Changes:
  aaa9947 feat: Add AWS SDK for DynamoDB and implement group services

## 0.17.2

### Patch Changes

- 0b74439: Changes:
  3e94d01 Enhance Profile Settings with i18n support and form validation
  84580c8 Remove sample modal footer buttons
  e784e31 Started applying zod and react hook form for profile settings modal

## 0.17.1

### Patch Changes

- 5e9f8ae: Changes:
  edc46ba Improved Account Settings state handling by moving them up to WorkspaceLayout Fixed issue for the Account Settings Overlay on close blocking the interactions Clean up a few logs

## 0.17.0

### Minor Changes

- 3e4d9ae: Changes:
  adf7be3 Upgraded nextui-org to heroui

## 0.16.4

### Patch Changes

- c8b9864: Changes:
  16c7159 ### Enhance Profile Settings Tab and Account Settings Modal functionality

## 0.16.3

### Patch Changes

- 9ba1ada: Changes:
  35c4eab feat: enhance AccountSettingsModal with structured tab configurations and dynamic rendering
  0a9dab6 prevent a few errors for iconSvg like stroke-width and clip-path props
  d8f81a9 feat: implemented permission based tabs visibility to prevent unauthorized users getting in
  c7ac4be Enhanced the `AccountSettingsModal` component to integrate role-based/workspace-based logic for tab visibility.
  03e790a feat: refactor ProfileSettingsTab to use TabLayout component for improved structure

## 0.16.2

### Patch Changes

- d60b488: Changes:
  b406e66 fix: Refactor AccountSettingsModal and AccountSettingsSidebar for improved layout and responsiveness
  e70e979 fix: Update AccountSettingsModal and AccountSettingsSidebar for improved responsiveness and accessibility

## 0.16.1

### Patch Changes

- 74c19e8: Changes:
  97d7842 feat: Implement lazy loading for Navbar and ProfileDropdown components

## 0.16.0

### Minor Changes

- 9d5c241: Changes:
  b357b63 Added IconName type the Tabs icons
  51999fc feat: Add Account Settings Sidebar and Profile Settings Tab components with styling updates Made a few enhancement to the scrollbar to be thinner (need extra UI design to be implemented)

## 0.15.4

### Patch Changes

- 0a26e41: Changes:
  0c308f6 feat: Improved Profile Dropdown menu UI for consistency based on design ui

## 0.15.3

### Patch Changes

- 67ff9f5: Changes:
  0b74e28 feat: add new flag icons and update language accordion

## 0.15.2

### Patch Changes

- c8cdd2b: Changes:
  e494e7d Fix onSelectionChange for the Accordion
  308a148 feat: update ProfileDropdown padding and enhance UserInfo with translations
  8e92189 feat: replace LanguageSwitcherMenu with LanguageAccordion and update ProfileDropdown styles
  d412811 feat: add UserInfo component and custom hover hook for improved user profile display

## 0.15.1

### Patch Changes

- df8c2b9: Changes:
  6f50fca Fix MenuItem type assignment in ProfileDropdown and add missing type for button items
  e398cb0 Remove debug log from renderItems function in ProfileDropdown
  503cd49 Refactor ProfileDropdown and ThemeSwitch for improved localization support and add missing translations \* Applied the translations for the profile dropdown menu

## 0.15.0

### Minor Changes

- 7142b01: Changes:
  553a6d1 add Contacts and Events pages with PortalUpperWorkspace layout PortalUpperWorkspace uses selected workspace from the store/state WorkspaceSelector avoid events and contacts to update the state on initial load

## 0.14.6

### Patch Changes

- 6b75b85: Changes:
  141c7bb Enhance ProfileDropdown and ThemeSwitch components with improved menu item handling and optional label display
  f81e9e2 Improved Select Item shared Class Added a better translation for the Dashboard world

## 0.14.5

### Patch Changes

- 90b6700: Changes:
  de88fef Improved and fixed the background color for the dropdowns and select when hovered Added a new constant selectItemClassName with classes to be used in Selects and Dropdowns items

## 0.14.4

### Patch Changes

- 3c8ae91: Changes:
  2548770 refactored Profile Dropdown Menu to be more smooth Replaced LanguageSwitcher with LanguageSwitcherMenu in ProfileDropdown Updated HoverDropdown to accept dynamic placement Improved Language Switcher for better look and smoothness

## 0.14.3

### Patch Changes

- 063d2e2: Changes:
  7183aad Remove console.log statements from AccountSettingsModal and ProfileDropdown components
  c037070 Fixed the Profile Settings won't open when Profile Menu Pressed
  5bfadff Added Account Settings Modal Added Account Settings Tabs component Added useAccountModal store to handle modal states Moved a few missing store to teh right place Made a few small refactors

## 0.14.2

### Patch Changes

- bb7abed: Changes:
  78a1b5a Renamed UserDropdown to ProfileDropdown
  8d8f63b _ Refactor UserDropdown and LanguageSwitcher components; _ implement useHoverableElement hook for improved dropdown behavior and remove unused console log in useAppStore
  dc139c7 Refactor content wrapper components: replace ContentWrapper with ContentWrapperBase, remove unused components, and update related imports
  00736a5 Refactor layout components to remove FloatingSidePanel and update Navbar implementation; delete unused ContentLayout and side-panel components

## 0.14.1

### Patch Changes

- ad10eec: Changes:
  7fe32c7 Refactor and rename files/folders to follow naming conventions
  fda1f9e Restructured and renamed components, adhering to the updated folder and file naming conventions outlined in the `README.md`.
  dc46fcb Applied naming convention to component SidePanel \* Renamed component folder from SidePanel to side-panel
  c9b8195 Encapsulated Portal pages into workspaces route group

## 0.14.0

### Minor Changes

- d6c890c: Changes:
  2f102cf Renamed the folders and files based on the naming convensions Included the index files to componetns and ts files

## 0.13.0

### Minor Changes

- f26a360: Changes:
  b74cfa4 finished up moving and reorganizing components
  045ceba Moved most of the public componetns to components/public/ ui and features Move some UI and features component for portal

## 0.12.0

### Minor Changes

- 4d9b4e1: Changes:
  87e1a43 refactor: Update import paths to use shared components
  1834ad4 feat: Started re-arrenging folder structure
  4b13873 Update README.md to clarify public and private section descriptions
  90bcc6d Add file and folder naming conventions to README Add Folder structure description and rules notes to RADME

## 0.11.10

### Patch Changes

- b9f25ca: Changes:
  3e28ade Refactor Select component styles and optimize className handling

## 0.11.9

### Patch Changes

- 33fbc56: Changes:
  d98134b Enhance FloatingSidePanel and WorkspaceLayout for improved layout consistency

## 0.11.8

### Patch Changes

- bd843bc: Changes:
  afdd608 Enhance UserDropdown with new preferences and accounts sections Add Receipt icon and update icons map Updated the Profile Menu with new values and it wip Fixed a few on hover styling for dark mode on Profile Menu

## 0.11.7

### Patch Changes

- 22b5815: Changes:
  3f62b52 fix: add class to DropdownItem for improved styling in WorkspacesSelector

## 0.11.6

### Patch Changes

- 0b8115a: Changes:
  0c5f8b4 fix: add biome-ignore comment for exhaustive dependencies linting in useLayoutEffect
  ffa6b51 fixed sidebar didn't open up after setting it to compactMode

## 0.11.5

### Patch Changes

- 42e80ab: Changes:
  e8b6f15 Refactor components and styles for improved consistency and performance
  765d741 Added a Select Group with Workspaces label for the WorkspaceSelector Update WorkspaceLayout and WorkspacesSelector for improved state management and performance; update localization files for consistency. Fixed a few biome warnings

## 0.11.4

### Patch Changes

- cb60ff6: Changes:
  5718ee4 Add optional fields to UserPreferences for workspace and academic year selection
  4f1c6cf Remove overlay property from user preferences and initial app store state
  b6119bd Refactor user and user preferences hooks to stateHandlers for better organization

## 0.11.3

### Patch Changes

- 9d15e71: Changes:
  e39b77f Refactor: Simplify user loading logic by removing unnecessary object merging
  91b503e Feature: Implement user preferences update functionality by a hook store handler
  e26e6d8 Refactor: Improve tenant state management and sidebar behavior in WorkspaceLayout

## 0.11.2

### Patch Changes

- b43c312: Changes:
  6234f25 Merge branch 'develop' of github.com:skoolscout/skoolscout-com into chore/user-preferences
  03464b4 Feature: Enhance language and user preference management in the app

## 0.11.1

### Patch Changes

- ef5cf97: Changes:
  25355c5 test 5.70
  8e4b45b remove ignore
  41626c6 fix typo
  3e53496 test 5.90
  0d5514b change version provider
  95819fe add conditional
  2d161e0 add 5.90 to infra
  9154397 add test commit

## 0.11.0

### Minor Changes

- 768e1ee: Changes:
  24f4537 Enhance loading skeleton layout with proper structure Enable minimap in editor
  d418a07 Enhance user state management by merging user updates only if changed
  a591c73 Added loading skeleton and user loading hooks Refactored loading states in components Set based for data workflow Temporately commented the Providers Suspense Added userHook to handle initial user data

## 0.10.8

### Patch Changes

- 330156c: Changes:
  072c90f Started updating custom classes values from px to rem
  64577e7 Add padding to WorkspacesSelector and Sidebar components for improved layout Fixed weird scrollbar on the sidebar
  44c6816 changes background overlay base color for darkmode to black
  c282689 Refactor WorkspacesSelector to simplify handleSelect function and improve event handling

## 0.10.7

### Patch Changes

- 1f2ad57: Changes:
  e900171 Update fetchJsonResponse to include credentials in fetch requests - Ensured cookies are sent with requests by adding 'credentials: "include"' to the fetch options.

## 0.10.6

### Patch Changes

- b2aafb6: Changes:
  4917af6 Enhance sidebar functionality with animation and layout adjustments - Added framer-motion for compact mode toggle animation, refined sidebar width handling, and improved layout consistency in Sidebar and SidebarListbox components.
  5427e94 Update sidebar compact mode width for improved layout spacing - Added a few extra missing pixels
  e7832b3 Fixed WorkspacesSelector icon spacing on Compact Mode

## 0.10.5

### Patch Changes

- 2369e11: Changes:
  32418be Update workspace names to include the Workspace within it using the translations

## 0.10.4

### Patch Changes

- 2c5ee51: Changes:
  691b8e2 Enhance and Fix the sidebar issue related to overlay mode

## 0.10.3

### Patch Changes

- 88fa635: Changes:
  8b168c2 Running Biome and auto fixing most of the files as possible

## 0.10.2

### Patch Changes

- 75d71ef: Changes:
  4eeda6e Refactor Navbar and UserDropdown components for improved mobile first UI
  c8ebcf0 Refactor Navbar and UserDropdown components for improved modularity
  130c752 Refactor Navbar component and introduce UserDropdown
  0a7684f Enhance component spacing and layout

## 0.10.1

### Patch Changes

- d99da4d: Changes:
  34ba509 Refactor various components for improved layout and styling
  86561b4 Refactor LanguageSwitcher and Navbar components for improved layout
  ad844c8 Enhance component styles

## 0.10.0

### Minor Changes

- c545ab4: Changes:
  9b8e9b1 Update dependencies in package-lock.json to latest versions
  48db475 Updated Nextjs to v15.2.3 to prevent the vulnerability CVE-2025-29927

## 0.9.6

### Patch Changes

- a28a3de: Changes:
  1209059 Refactor WorkspaceLayout and Sidebar components for improved functionality and responsiveness

## 0.9.5

### Patch Changes

- 3654ed9: Changes:
  dd91f76 Refactor Navbar and StudentSelector components for improved styling and layout
  abbe34a Update component styles for consistency and improved layout

## 0.9.4

### Patch Changes

- ae0c8b1: Changes:
  d6fdfd2 Enhance Navbar component styling for better mobile experience

## 0.9.3

### Patch Changes

- 6f1cf71: Changes:
  b2695d2 Update useMediaQuery hook to use window.outerWidth for accurate width detection

## 0.9.2

### Patch Changes

- f5be508: Changes:
  a37253b Update AcademicYearSelector and StudentSelector to display academic year in YY format
  dc7e7a1 Refactor StudentSelector and Navbar components for improved loading state and selection handling
  a41020e Refactor WorkspaceLayout, WorkspacesSelector, and Navbar components for improved state management and loading handling

## 0.9.1

### Patch Changes

- 98174ba: Changes:
  6ca42a3 Updated useMediaQuery hook to have width references on initial load Updated WorkspaceLayout navbar menu to work on first load of the page

## 0.9.0

### Minor Changes

- ad1f628: Changes:
  43fa032 Refactor media query handling in WorkspaceLayout and Sidebar components

## 0.8.4

### Patch Changes

- aaeba2a: Changes:
  42fc233 Refactor Navbar component for improved mobile layout and update Tailwind breakpoints

## 0.8.3

### Patch Changes

- d0e5c1c: Changes:
  507fef1 Made changes into the tailwind config file ti include the layout screen sizes
  5517a1f Refactor mobile responsiveness by introducing maxMobileWidth utility and updating media queries across components

## 0.8.2

### Patch Changes

- a865e60: Changes:
  01aaec6 Refactor Navbar components for Mobile and general improved for responsiveness and styling

## 0.8.1

### Patch Changes

- 50e0cf4: Changes:
  cba7bf4 Refactor getVersionInfo to directly import package.json for version retrieval
  b6d5700 Merge branch 'develop' of github.com:skoolscout/skoolscout-com into chore/improve-appstore-versioning
  6d11b4e Refactor version handling and integrate getVersionInfo utility across components

## 0.8.0

### Minor Changes

- 702bb7e: Changes:
  76c9d81 refactor: optimize imports and clean up unused state variables in WorkspaceLayout Improved WorkspaceLayout by applying a few good practices
  2a8faab feat: add AcademicYearSelector component and integrate it into Navbar

## 0.7.4

### Patch Changes

- d4e2020: Changes:
  99c4a2e remove test comment
  42ba2cc change run time

## 0.7.3

### Patch Changes

- ec21cb5: Changes:
  78cfe30 Refactor MainPortalNavbar and Navbar components to remove startContent prop and integrate triggerSidebar functionality for improved mobile navigation
  bb992c1 Refactor WorkspacesSelector and Navbar components to improve user role handling and display
  ea8c2f1 Refactor user handling in sidebar and navbar components; add user service for fetching logged-in user data
  ac9b032 Remove unused academic years and students API routes; refactor getStudentsByAcademicYears to return mock data

## 0.7.2

### Patch Changes

- 1bf4fef: Changes:
  20cbdc1 add test commnet
  3b6c545 ``` Update EventBridge schedule, cleanup CI, and add ECS commands

## 0.7.1

### Patch Changes

- 0fa6d89: Changes:
  6a2dd27 remove test comment
  2c22468 Add EventBridge scheduler for ECS service shutdown

## 0.7.0

### Minor Changes

- 40f4eea: Changes:
  efa3a24 Moved ContentWrapper imports for consistency across portal components
  ff500c1 Moved ContentWrapper components and update imports for consistency
  2e38d17 Remove Sidebar and related components from the portal
  bb9d130 Moved Workspace selecter for a better adecuate place
  d5d4582 Add navbar components and menu items for portal interface
  a38b3d1 Enhance workspace selection & Include translations Improved Navbar Menu Items, naming, placement Improve mobile version of the navbar Made navbar avatar bigger on mobile and adapted the width of its container Improved Workspace selector to save the workspace ont he app store Change how the Navbar menu items are displayes based on the allowed workspaces for each menu item Applied translations for the menu labels

## 0.6.1

### Patch Changes

- 453ff4c: Changes:
  c4f125c fix: add open/close state management to StudentSelector component
  1760f90 fix: enhance NavBarMenuItem and StudentSelector styling for improved hover and focus states
  30eb39c fix: update NavBarMenuItem styling and improve color definitions in Tailwind config
  5b92520 fix: enhance StudentSelector component with improved type handling and styling
  5224ec3 fix: improve styling and accessibility for StudentSelector component

## 0.6.0

### Minor Changes

- 3e8fcac: Changes:
  81c95b8 feat: update theming support and version bump to 0.5.7

## 0.5.7

### Patch Changes

- fc84afb: Changes:
  346630d fix: correct typo in classNames for SidebarListbox component
  a3e14b8 fix: update SidebarListbox styles and adjust foreground color in Tailwind config
  3515fd6 feat: add dark logo support and adjust sidebar spacing

## 0.5.6

### Patch Changes

- 6909e0e: Changes:
  af5fa77 fix: remove header content from WorkspaceLayout story
  e32b888 fix: comment out Navbar component in admin layout
  1c15d75 fix: update Navbar and layout components for improved styling and structure

## 0.5.5

### Patch Changes

- 68f871f: Changes:
  f078480 fix(tailwind): update color keys to use DEFAULT for content values

## 0.5.4

### Patch Changes

- 27680d8: Changes:
  dca36b4 add test commit to test ci

## 0.5.3

### Patch Changes

- a770f28: Changes:
  ac35d5b Refactor Navbar component: enhance props handling and improve menu rendering for better responsiveness Render menu on tablet and mobile inside user menu
  c33c763 Refactor WorkspaceLayout and layout components: remove MainPortalNavbar from layout and enhance button styling in WorkspaceLayout
  f273e17 Refactor WorkspaceLayout and Navbar components: integrate MainPortalNavbar for improved header structure and user experience
  0e143e6 Refactor NavBarMenuItem and MainPortalNavbar: replace FontAwesome icons with custom Icon component for improved consistency and maintainability
  8f2c3aa Refactor BaseRoundedButton and NavBarMenuItem: enhance button styling and optimize rendering with useMemo
  7419965 Refactor StudentSelector component: replace BaseRoundedButton with a styled span for improved accessibility and styling
  a891c64 Refactor student services and components: remove unused files, update imports, and enhance student selection logic Updated the Student Selector UI Design

## 0.5.2

### Patch Changes

- 0e83063: Changes:
  59721ce test new runner

## 0.5.1

### Patch Changes

- b080199: Changes:
  89245d9 Update default color for content1 in Tailwind CSS configuration
  ad217d5 Update Tailwind CSS color palette for default colors with the new cold grayshade

## 0.5.0

### Minor Changes

- a18609c: Changes:
  dd85116 Refactor navbar components: replace ParentsNavbar with MainPortalNavbar and introduce NavbarWrapper for improved structure

## 0.4.1

### Patch Changes

- 2c58531: Changes:
  c61f294 Update WorkspacesSelector and Sidebar components for improved styling

## 0.4.0

### Minor Changes

- 958dada: Changes:
  d0b35a2 Refactor API route handlers to remove unnecessary imports and improve type usage
  5049ef0 Update background color in sandbox components to use bg-background class
  f667470 Add messages API and message service for fetching messages
  d823599 Added students API by userId and return dummy data Created students service to retrievve the Api data Updated appStore to store the students data Made data improvements

## 0.3.2

### Patch Changes

- a499204: Changes:
  408c90e add test commit
  eb73070 Add DynamoDB module for team and messages tables

## 0.3.1

### Patch Changes

- 71be73e: Changes:
  8810f78 Refactor Tailwind configuration to add the missing color definitions for success, warning, and danger states, and improve background and foreground color settings.
  2d5a1b1 Update Tailwind configuration with new color definitions for missing colors Refactor Navbar and Sidebar components for improved styling
  c827ac2 Move the themes into nextui tailwind config file Fixed issue with theme not working and improve how the theme look Refactor theme handling and clean up unused CSS variables
  873049a Add selectedWorkspace state and setSelectedWorkspace action to AppStore
  f68d37d Enhance WorkspacesSelector to utilize user roles from app store and add schools endpoint

## 0.3.0

### Minor Changes

- be0c693: Changes:
  1fc4be0 Update Sidebar button icon color to use default color variable
  094385b Enhance Tailwind CSS color configuration based on Figma System Design Update sidebar component styles;
  6d243bf Remove unnecessary divider logic from SidebarListbox component
  dda97a0 Improved and fixed Menu section divider and spacing on the sidebar menu
  c5db745 Refactor sidebar menu items and update CSS variable names for consistency

## 0.2.10

### Patch Changes

- 0b0db19: Changes:
  e3f714d Adjust spacing and margin in Sidebar and SidebarListbox components for improved layout consistency
  4b2c96d Refactor Sidebar component to extract SidebarHeader for improved readability and maintainability
  a0970a4 Made svg take strokes come from css classes Remove unnecessary text color classes from SidebarListbox for cleaner styling
  e51fbdd Refactor components to use Icon component for improved consistency and maintainability; add dotenv configuration files for app-ui and app-service.

## 0.2.9

### Patch Changes

- 0e43a2a: Changes:
  169f4e4 test commit to verify ci work fine

## 0.2.8

### Patch Changes

- 9f21820: Changes:
  65d2ea9 add change to debug
  ea409b6 Add `.infra` package and include it in workspaces
  c158f49 test commit

## 0.2.7

### Patch Changes

- 74111ed: Changes:
  ae322ed test commit

## 0.2.6

### Patch Changes

- b5cb3cf: Changes:
  dc6a1df Update text color in globals.css and tailwind.config.js
  1c703b7 adding background color where missing and trying the text-color for the global tailwind
  8b7d6b8 chore/portal-theming

## 0.2.5

### Patch Changes

- 2fe9faa: Changes:
  e2de76a Optimize section and item class calculations in showDividers Optimized using useMemo for performance improvements
  70ac648 Fix spelling errors in sidebar menu items and update icons for consistency for unnasigned items
  bee1a47 Update sidebar menu icons for consistency across roles
  611f665 Added dashboards and sidebar menu items for Admin, School Counselor, and Educational Consultant
  06d8a0e Updated staff sidebar menu items with the latest version
  576636f Add School Coach dashboard and sidebar menu items; update workspaces to include the new role for School Coach and translations
  d17a7d6 Update sidebar translations for "School Rep" to "School Reps" for consistency
  1b957e3 Update sidebar translations for parents and candidates for consistency and clarity
  95fb33d Refactor SidebarListbox to use NestItem component for nested items instead of useCallback

## 0.2.4

### Patch Changes

- 4bbc174: Changes:
  e0af9b9 Add SearchIcons component and storybook

## 0.2.3

### Patch Changes

- b46b876: Changes:
  96b1082 feat: Add school counselor sidebar menu with localization support
  d3b10cd feat: Add admission officer sidebar menu with localization support
  3c46a4a feat: Add school representative sidebar menu with localization support
  e30244f Moved and renamed candidate sidebar menu
  1630705 feat: Refactor sidebar menus for counselor, parent, and staff with new structure and localization support
  83abd73 Added counselor sidebar menu with localization support
  4fb70ff feat: Implement candidate and parent sidebar menus with routing functionality
  1af516f refactor: Remove Suspense wrapper from layout components for improved performance
  b8e9e4e refactor: Update sidebar and navbar components to use FontAwesome icons and improve localization strings
  345e501 _ Made small refactory to Icon component and IconNames interface _ Change the use of FontAwesome on the sidebar to use our Icon component _ Updated the SVG components to accept extra props to be customized _ Updated parents sidebar to use the new icons
  bd0a073 fix: Update dashboard titles for counselor and staff pages
  71f5213 feat: Added basic dashboard pages for each workspace Added Translation for WorkspaceSelector

## 0.2.2

### Patch Changes

- 00ba4d2: Changes:
  bf3722a Remove unused exports from ParentsSidebar and clean up WorkspaceLayout stories
  fa21154 Wrap LanguageWithProvider in Suspense for improved loading state handling
  9c8cbe5 Remove unused sample exports from WorkspaceLayout stories
  9138de1 Enhance LanguageSwitcher and ParentsNavbar with improved translations and accessibility
  9021e02 Refactor sidebar items for parents and staff with updated translation keys Included Staff Sidebar data into translations
  540815b Applied translation to the sidebar items
  37ba0ee Improve button styling in LanguageSwitcher for better accessibility
  cc552ed Add language support with LanguageSwitcher component and context provider and i18n
  3d2a6a7 Adding language handler
  c13c355 Added Navbar menu for User menu; Added basic Theme changer and Menu for Languages Created HoverDropdown were open is trigger by hover Implemented HoverDropdown for Language menu

## 0.2.1

### Patch Changes

- c82e311: Changes:
  ea1a297 Add missing icons and icons page

## 0.2.0

### Minor Changes

- ed6adca: Changes:
  aae875b Remove Icon from register page
  dff0bee Add icons svg and icon component

## 0.1.14

### Patch Changes

- f204283: Changes:
  25142ca Fixing width and height of sidebar items icon areas Added the right font sizes to menu items Added separated Providers implementation depending on the area, so we can use different themes and separated
  b4dbc5c Refactor UI components: Adjust button and icon sizes, update sidebar styles for better responsiveness

## 0.1.13

### Patch Changes

- 9991ec1: Changes:
  2fb142d Refactor WorkspaceLayout: Comment out localStorage updates and adjust sidebar width

## 0.1.12

### Patch Changes

- 917a7d5: Changes:
  c02074e feat: set the overlaymode closed after coming back to desktop size; enhance SidebarListbox and SidebarWrapper with improved rendering and logging
  4f1d936 feat: update SidebarWrapper to use flex layout for improved responsiveness
  6615d94 feat: enhance SidebarWrapper with improved Modal structure and conditional rendering
  3b80b8c feat: update sidebar styles and simplify Modal component structure Fixed overlay sidebar lost fonts
  f61613d feat: update font weight in WorkspacesSelector and SidebarListbox for improved readability
  241413c feat: update icon in WorkspacesSelector and refine styles
  2d0a65a feat: update font styles and improve workspace selector labels

## 0.1.11

### Patch Changes

- a6ba1de: Changes:
  4ea841b feat: Update sidebar item styles for improved light and dark theme support
  43e19b9 feat: Update sidebar structure and improve theme configuration
  12dd25b feat: Refactor WorkspaceLayout imports and add Storybook stories for component testing
  1c5b30f feat: Enhanced tenant management and sidebar integration with dynamic logo support; Added IYM Logo

## 0.1.10

### Patch Changes

- 7e5f49c: Changes:
  0be6f84 remove test comment

## 0.1.9

### Patch Changes

- f9cceb5: Changes:
  0d71b93 Handle backend fetch errors in version page.
  0a6f4bb commit only to up app-services version

## 0.1.8

### Patch Changes

- 35c5daf: Changes:
  7544460 Add user role-based workspace selection in WorkspacesSelector component
  d151958 Refactor WorkspacesSelector and Sidebar components for improved layout and accessibility; update icon sizes and remove unused localStorage calls
  456feb4 Adjust sidebar translation for overlay mode in SidebarWrapper component
  7cbcd09 Enhance staff portal layout with Suspense and new sidebar items; update icons and workspace selector

## 0.1.7

### Patch Changes

- 3fa8c74: Changes:
  a5e5a20 Update environment variable for backend URL reference

## 0.1.6

### Patch Changes

- f0f9d30: Changes:
  a990ccd Add force dynamic page
  d84bdd3 Add error handling for database connection failures
  74d9492 Add version info page for UI and backend

## 0.1.5

### Patch Changes

- d2359ee: Changes:
  8803f37 feat: add dropdown support to NestedItemMenu for compact mode
  f400f49 feat: enhance WorkspacesSelector with dropdown support and icon integration on compact mode

## 0.1.4

### Patch Changes

- 7410891: Changes:
  d76d2f2 Added and cleaned up items for benefit of developers.
  c76206e Merge remote-tracking branch 'origin/develop' into develop
  3a8faf2 Implemented landing page

## 0.1.3

### Patch Changes

- b0bc0ad: Changes:
  457cecb fix deploy app-ui

## 0.1.2

### Patch Changes

- 69a1582: Changes:
  8577cb8 remove test comment

## 0.1.1

### Patch Changes

- a85f34e: Changes:
  c1d87bc add script to conditinal deploy a test on readme

## 0.1.0

### Minor Changes

- 111293b: Changes:
  061d4cc Merge branch 'develop' of github.com:skoolscout/skoolscout-com into feature/sidebar-logic-v2
  82cf29c Remove unused ParentsSidebar import from staff layout and clean up layout component
  2c2c5ff Fixed a few inconsistences Rename SidebarMenu to SidebarItems Created bette SidebarItem menu list Added example sidebarItem menu list for the stories examples Improved WorkspaceLayout and handle better behavor and logics and render correctly Clean up some code
  d08629a Extracted parentsMenu SidebarMenu into its own file Improved menu workflow from workspace layout down Improved Fixed Some stories error Cleaned up a little bit
  02e7487 Add NestedItemMenu component to enhance sidebar item rendering
  ae4c5b7 Refactor sidebar components to use 'href' instead of 'link' and introduce CompactSidebarListItem for improved rendering
  c485faf Add ParentsMenu structure and update Sidebar to support nested menu items
  f66a8ad Add sideBarMenu prop to WorkspaceLayout and Sidebar components

## 0.0.3

### Patch Changes

- 7c9aeba: Changes:
  abab96a remove test commet

## 0.0.2

### Patch Changes

- 0b7dd0c: Changes:
  56ef54a Update versioning and documentation across packages
